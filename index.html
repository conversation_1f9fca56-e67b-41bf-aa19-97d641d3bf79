<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>knowledge-builder</title>
    <meta name="description" content="Create, manage and take educational tests with AI-powered question generation" />
    <meta name="author" content="mQuiz" />
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#3b82f6" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="mQuiz" />
    <meta name="mobile-web-app-capable" content="yes" />
    
    <!-- Manifest -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="192x192" href="/icon-192.png" />
    <link rel="icon" type="image/png" sizes="512x512" href="/icon-512.png" />
    <link rel="apple-touch-icon" href="/icon-192.png" />

    <meta property="og:title" content="mQuiz" />
    <meta property="og:description" content="Create, manage and take educational tests with AI-powered question generation" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/icon-512.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="mQuiz" />
    <meta name="twitter:description" content="Create, manage and take educational tests with AI-powered question generation" />
    <meta name="twitter:image" content="/icon-512.png" />
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
