@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 210 20% 98%;
    --foreground: 220 13% 18%;

    --card: 0 0% 100%;
    --card-foreground: 220 13% 18%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 18%;

    --primary: 214 84% 56%;
    --primary-foreground: 210 40% 98%;

    --secondary: 214 32% 91%;
    --secondary-foreground: 220 13% 18%;

    --muted: 214 32% 91%;
    --muted-foreground: 220 9% 46%;

    --accent: 142 76% 36%;
    --accent-foreground: 210 40% 98%;

    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;
    
    --warning: 38 92% 50%;
    --warning-foreground: 210 40% 98%;
    
    --quiz: 262 83% 58%;
    --quiz-foreground: 210 40% 98%;
    
    --gradient-primary: linear-gradient(135deg, hsl(214, 84%, 56%), hsl(262, 83%, 58%));
    --gradient-success: linear-gradient(135deg, hsl(142, 76%, 36%), hsl(142, 76%, 46%));
    --gradient-subtle: linear-gradient(180deg, hsl(210, 20%, 98%), hsl(214, 32%, 95%));
    
    --shadow-elegant: 0 10px 30px -10px hsl(214 84% 56% / 0.3);
    --shadow-glow: 0 0 40px hsl(214 84% 56% / 0.2);

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    /* Prevent zoom on double tap for mobile */
    touch-action: manipulation;
  }
  
  /* Touch-friendly scrolling */
  * {
    -webkit-overflow-scrolling: touch;
  }
  
  /* Better touch targets */
  button, a, [role="button"] {
    touch-action: manipulation;
    min-height: 44px;
    min-width: 44px;
  }
  
  @media (min-width: 768px) {
    button, a, [role="button"] {
      min-height: auto;
      min-width: auto;
    }
  }
}