import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>rigger } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Menu } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

interface MobileDrawerProps {
  children: React.ReactNode;
  title?: string;
  trigger?: React.ReactNode;
}

export const MobileDrawer = ({ children, title = "Menu", trigger }: MobileDrawerProps) => {
  const isMobile = useIsMobile();

  if (!isMobile) {
    return <>{children}</>;
  }

  return (
    <Sheet>
      <SheetTrigger asChild>
        {trigger || (
          <Button variant="ghost" size="sm" className="md:hidden">
            <Menu className="w-5 h-5" />
          </Button>
        )}
      </SheetTrigger>
      <SheetContent side="left" className="w-80">
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
        </She<PERSON>Header>
        <div className="mt-6">
          {children}
        </div>
      </SheetContent>
    </Sheet>
  );
};