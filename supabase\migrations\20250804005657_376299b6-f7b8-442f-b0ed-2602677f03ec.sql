-- Fix the security warning by setting search_path for handle_new_user function
CREATE OR REP<PERSON>CE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, user_id, email, full_name, role, is_approved)
  VALUES (
    gen_random_uuid(),
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
    COALESCE((NEW.raw_user_meta_data->>'role')::text, 'parent'),
    CASE 
      WHEN COALESCE((NEW.raw_user_meta_data->>'role')::text, 'parent') = 'admin' THEN false 
      ELSE true 
    END
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = 'public';